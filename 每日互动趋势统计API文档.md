# 每日互动趋势统计API文档

## 接口概述
该接口用于获取展会期间每日的互动趋势数据，包括展商扫码数量和预约数量的变化趋势。

## 接口详情

### 请求信息
- **URL**: `/v1/expo/interaction/statistics/trend`
- **Method**: `GET`
- **Content-Type**: `application/json`

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| expoId | Integer | 是 | 展会ID |

### 请求示例
```bash
GET /v1/expo/interaction/statistics/trend?expoId=1
```

### 响应格式

#### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "dateList": ["6月15日", "6月16日", "6月17日", "6月18日"],
    "exhibitorScanData": [1200, 1600, 1100, 1000],
    "appointmentData": [300, 400, 350, 250]
  }
}
```

#### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.dateList | Array[String] | 日期列表，格式为"M月d日" |
| data.exhibitorScanData | Array[Integer] | 展商扫码数据，与dateList一一对应 |
| data.appointmentData | Array[Integer] | 预约数据，与dateList一一对应 |

#### 错误响应
```json
{
  "code": 400,
  "message": "展会ID不能为空",
  "data": null
}
```

```json
{
  "code": 404,
  "message": "展会不存在",
  "data": null
}
```

## 数据说明

### 统计规则
1. **时间范围**: 基于展会的开始时间和结束时间
2. **数据来源**: 
   - 展商扫码数据来自 `ech_expo_exhibitor_scan_record` 表
   - 预约数据来自 `ech_expo_appointment` 表
3. **统计维度**: 按天统计，以创建时间的日期部分为准
4. **数据过滤**: 只统计未删除的记录 (`is_deleted = 0`)
5. **公司隔离**: 只统计当前用户所属公司的数据

### 数据对齐
- 如果某天没有扫码或预约数据，对应位置会填充0
- 日期列表覆盖展会的完整时间范围
- 三个数组（dateList、exhibitorScanData、appointmentData）长度相同，索引一一对应

## 使用场景
1. **展会数据分析**: 分析展会期间每日的互动活跃度
2. **趋势监控**: 监控展商扫码和预约的变化趋势
3. **数据可视化**: 为前端图表提供数据支持
4. **运营决策**: 基于互动数据制定运营策略

## 前端集成示例

### JavaScript/Vue.js
```javascript
// API调用
async function getInteractionTrend(expoId) {
  try {
    const response = await fetch(`/v1/expo/interaction/statistics/trend?expoId=${expoId}`);
    const result = await response.json();
    
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取互动趋势数据失败:', error);
    throw error;
  }
}

// 使用示例
getInteractionTrend(1).then(data => {
  console.log('日期列表:', data.dateList);
  console.log('展商扫码数据:', data.exhibitorScanData);
  console.log('预约数据:', data.appointmentData);
  
  // 可以直接用于图表库如 ECharts
  const chartOption = {
    xAxis: {
      data: data.dateList
    },
    series: [
      {
        name: '展商扫码',
        type: 'line',
        data: data.exhibitorScanData
      },
      {
        name: '预约',
        type: 'line', 
        data: data.appointmentData
      }
    ]
  };
});
```

## 注意事项
1. 需要用户登录，接口会自动获取当前用户的公司ID
2. 展会ID必须存在且有效
3. 返回的日期格式为中文格式，便于前端直接显示
4. 数据按创建时间统计，不是按业务发生时间
5. 接口性能已优化，支持大数据量查询

## 版本历史
- v1.0: 初始版本，支持基本的每日互动趋势统计
