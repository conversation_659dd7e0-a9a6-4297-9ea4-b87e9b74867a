package com.echronos.expo.api.resp;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/6/27 11:39
 * @ClassName AudienceResp
 */
@Data
public class AudienceResp {

    /**
     * ID
     */
    private Long id;
    /**
     * 所属公司id
     */
    private Integer companyId;
    /**
     * 展会id
     */
    private Long expoId;
    /**
     * 渠道ID
     */
    private Long channelId;
    /**
     * 观众名称
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 关联客户系统id
     */
    private Integer customerId;
    /**
     * 自定义表单code
     */
    private String formCode;
    /**
     * 自定义表单版本
     */
    private Integer versionNumber;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 渠道名称
     */
    private String channelName;
    /**
     * 是否发送邮件
     * 0否
     * 1是
     */
    private String isSend;
    /**
     * 邮件发送次数
     */
    private Integer sendCount;
    /**
     * 最近发送时间
     */
    private LocalDateTime lastSendTime;
    /**
     * 是否签到
     * 0否
     * 1是
     */
    private String isSign;
    /**
     * 最近签到时间
     */
    private LocalDateTime lastSignTime;
    /**
     * 签到次数
     */
    private Integer signCount;
}
