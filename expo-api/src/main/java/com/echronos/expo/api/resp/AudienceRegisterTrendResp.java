package com.echronos.expo.api.resp;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 观众报名统计
 *
 * <AUTHOR>
 * @Date 2025/6/27 14:25
 * @ClassName AudienceCountResp
 */
@Data
public class AudienceRegisterTrendResp {

    /**
     * 展会注册报名总人数
     */
    private Integer registerCount;

    /**
     * 渠道注册报名人数
     */
    private List<ChannelCountResp> channelCountList;

    /**
     * 报名日期
     */
    private LocalDate date;
}
