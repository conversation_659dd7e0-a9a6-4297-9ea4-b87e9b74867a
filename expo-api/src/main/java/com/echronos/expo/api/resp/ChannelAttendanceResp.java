package com.echronos.expo.api.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 渠道注册人数
 *
 * <AUTHOR>
 * @Date 2025/6/27 14:34
 * @ClassName ChannelCountResp
 */
@Data
public class ChannelAttendanceResp {

    /**
     * 渠道ID
     */
    private Long channelId;
    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 到场人数
     */
    private Integer attendanceCount;


    /**
     * 到场率
     */
    private BigDecimal attendanceRate;
}
