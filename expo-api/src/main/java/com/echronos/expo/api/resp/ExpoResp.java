package com.echronos.expo.api.resp;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 展会信息
 *
 * <AUTHOR>
 * @Date 2025/6/25 17:57
 * @ClassName ExpoResp
 */
@Data
public class ExpoResp {

    /**
     * ID
     */
    private Long id;
    /**
     * 所属公司id
     */
    private Integer companyId;
    /**
     * 展会名称
     */
    private String expoName;
    /**
     * 展会简称
     */
    private String shortName;
    /**
     * 国家code
     */
    private Integer countryCode;
    /**
     * 城市code
     */
    private Integer cityCode;

    /**
     * 城市时区
     */
    private String zoneId;
    /**
     * 展馆名称
     */
    private String hallName;

    /**
     * 举办方名称
     */
    private String organizer;

    /**
     * 详细地址
     */
    private String address;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 城市所在时区开始时间
     */
    private LocalDateTime zoneIdStartTime;
    /**
     * 城市所在时区结束时间
     */
    private LocalDateTime zoneIdEndTime;
    /**
     * 展会状态：0、未开始；1、进行中；2、已结束
     */
    private Integer expoStatus;
    /**
     * remark
     */
    private String remark;
}
