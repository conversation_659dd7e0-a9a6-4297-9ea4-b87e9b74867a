package com.echronos.expo.api.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 报名/到场分析
 *
 * <AUTHOR>
 * @Date 2025/6/27 16:56
 * @ClassName AudienceStatisticsResp
 */
@Data
public class AudienceStatisticsResp {

    /**
     * 展会注册报名总人数
     */
    private Integer registerCount;

    /**
     * 展会渠道注册报名人数
     */
    private List<ChannelCountResp> channelCountList;
    /**
     * 观众注册统计数据趋势列表
     */
    private List<AudienceRegisterTrendResp> audienceRegisterTrendList;

    /**
     * 到场总人数
     */
    private Integer attendanceCount;

    /**
     * 未到场人数
     */
    private Integer unAttendanceCount;
    /**
     * 到场总人次
     */
    private Integer attendanceSum;
    /**
     * 到场率
     */
    private BigDecimal attendanceRate;

    /**
     * 渠道到场统计
     */
    private List<ChannelAttendanceResp> channelAttendanceList;
    /**
     * 观众到场趋势
     */
    private List<AudienceSignTrendResp> audienceSignTrendList;

}
