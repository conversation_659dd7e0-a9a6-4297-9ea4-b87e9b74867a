# 每日互动趋势统计接口实现说明

## 概述
根据提供的原型图，实现了展会每日互动趋势统计接口，用于统计展会期间每天的展商扫码和预约数据。

## 接口信息
- **接口路径**: `GET /v1/expo/interaction/statistics/trend`
- **接口描述**: 获取展会期间每日互动趋势数据
- **参数**: `expoId` (展会ID，必填)

## 实现内容

### 1. 新增VO类
**文件**: `expo-service/src/main/java/com/echronos/expo/vo/ExpoInteractionTrendVO.java`
```java
@Data
public class ExpoInteractionTrendVO {
    private List<String> dateList;           // 日期列表 (如: ["6月15日", "6月16日"])
    private List<Integer> exhibitorScanData; // 展商扫码数据
    private List<Integer> appointmentData;   // 预约数据
}
```

### 2. 数据访问层扩展

#### ExpoExhibitorScanRecordDao
新增方法：
```java
List<Map<String, Object>> countScanRecordsByDate(@Param("expoId") Integer expoId, @Param("companyId") Integer companyId);
```

#### ExpoAppointmentDao  
新增方法：
```java
List<Map<String, Object>> countAppointmentsByDate(@Param("expoId") Integer expoId, @Param("companyId") Integer companyId);
```

### 3. SQL查询实现

#### 展商扫码统计 (ExpoExhibitorScanRecordMapper.xml)
```sql
SELECT 
    DATE(create_time) as scan_date,
    COUNT(*) as scan_count
FROM ech_expo_exhibitor_scan_record
WHERE expo_id = #{expoId}
  AND company_id = #{companyId}
  AND is_deleted = 0
GROUP BY DATE(create_time)
ORDER BY scan_date
```

#### 预约统计 (ExpoAppointmentMapper.xml)
```sql
SELECT 
    DATE(create_time) as appointment_date,
    COUNT(*) as appointment_count
FROM ech_expo_appointment
WHERE expo_id = #{expoId}
  AND company_id = #{companyId}
  AND is_deleted = 0
GROUP BY DATE(create_time)
ORDER BY appointment_date
```

### 4. 业务逻辑实现

#### Manager层扩展
- **ExpoExhibitorScanRecordManager**: 新增 `getScanRecordsByDate()` 方法
- **ExpoAppointmentManager**: 新增 `getAppointmentsByDate()` 方法

#### Service层实现
**ExpoInteractionStatisticsServiceImpl.interactionTrendStatistics()**:

1. 根据展会ID获取展会信息
2. 计算展会期间的日期范围 (startTime 到 endTime)
3. 生成日期列表，格式为 "M月d日"
4. 查询展商扫码数据，按日期分组统计
5. 查询预约数据，按日期分组统计
6. 将数据按日期对齐，缺失日期补0
7. 返回趋势数据

### 5. 控制器更新
更新返回类型从 `ExpoAppointStatusVO` 到 `ExpoInteractionTrendVO`

## 数据表依赖

### 主要表结构
1. **ech_expo_info** - 展会信息表
   - 用于获取展会的开始和结束时间
   
2. **ech_expo_exhibitor_scan_record** - 展商观众扫码记录表
   - expo_id: 展会ID
   - create_time: 扫码时间
   - company_id: 公司ID
   - is_deleted: 删除标记

3. **ech_expo_appointment** - 预约表
   - expo_id: 展会ID  
   - create_time: 预约时间
   - company_id: 公司ID
   - is_deleted: 删除标记

## 返回数据格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "dateList": ["6月15日", "6月16日", "6月17日", "6月18日"],
    "exhibitorScanData": [1200, 1600, 1100, 1000],
    "appointmentData": [300, 400, 350, 250]
  }
}
```

## 测试
创建了单元测试文件 `ExpoInteractionStatisticsServiceTest.java`，包含：
- 正常情况下的趋势统计测试
- 展会不存在时的异常处理测试

## 特性
1. **日期对齐**: 确保所有日期都有对应数据，缺失数据补0
2. **时间范围**: 基于展会的实际开始和结束时间
3. **数据完整性**: 只统计未删除的有效记录
4. **公司隔离**: 按公司ID进行数据隔离
5. **格式化**: 日期格式化为中文显示格式 "M月d日"

## 使用示例
```bash
GET /v1/expo/interaction/statistics/trend?expoId=1
```

该接口完全基于现有的表结构实现，无需额外的数据库变更，符合原型图的展示需求。
