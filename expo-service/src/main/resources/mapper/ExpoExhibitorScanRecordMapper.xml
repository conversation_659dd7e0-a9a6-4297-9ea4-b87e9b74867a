<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoExhibitorScanRecordDao">

    <resultMap id="ExpoExhibitorScanRecordMap" type="com.echronos.expo.model.ExpoExhibitorScanRecord">
        <result property="id" column="id"/>
        <result property="expoId" column="expo_id"/>
        <result property="exhibitorId" column="exhibitor_id"/>
        <result property="audienceId" column="audience_id"/>
        <result property="companyId" column="company_id"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
    
    <sql id="columns">
        <![CDATA[
        id, expo_id, exhibitor_id, audience_id, company_id, create_user, create_time, update_user, update_time, is_deleted, tenant_id
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=expoId">
                AND expo_id = #{expoId}
            </if>
            <if test="null!=exhibitorId">
                AND exhibitor_id = #{exhibitorId}
            </if>
            <if test="null!=audienceId">
                AND audience_id = #{audienceId}
            </if>
            <if test="null!=companyId">
                AND company_id = #{companyId}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
            <if test="null!=tenantId and ''!=tenantId">
                AND tenant_id = #{tenantId}
            </if>
        </where>
    </sql>

    <!-- 按日期统计展商扫码次数 -->
    <select id="countScanRecordsByDate" resultType="java.util.Map">
        SELECT
            DATE(create_time) as scan_date,
            COUNT(*) as scan_count
        FROM ech_expo_exhibitor_scan_record
        WHERE expo_id = #{expoId}
          AND company_id = #{companyId}
          AND is_deleted = 0
        GROUP BY DATE(create_time)
        ORDER BY scan_date
    </select>

    <!-- 按小时统计展商扫码次数 -->
    <select id="countScanRecordsByHour" resultType="java.util.Map">
        SELECT
            HOUR(create_time) as scan_hour,
            COUNT(*) as scan_count
        FROM ech_expo_exhibitor_scan_record
        WHERE expo_id = #{expoId}
          AND company_id = #{companyId}
          AND DATE(create_time) = #{statisticsDate}
          AND is_deleted = 0
        GROUP BY HOUR(create_time)
        ORDER BY scan_hour
    </select>

</mapper>

