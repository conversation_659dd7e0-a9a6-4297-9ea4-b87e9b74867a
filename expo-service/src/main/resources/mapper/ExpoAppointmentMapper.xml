<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.echronos.expo.dao.ExpoAppointmentDao">

    <resultMap id="ExpoAppointmentMap" type="com.echronos.expo.model.ExpoAppointment">
        <result property="id" column="id"/>
        <result property="expoId" column="expo_id"/>
        <result property="businessId" column="business_id"/>
        <result property="businessType" column="business_type"/>
        <result property="appointedPersonnelId" column="appointed_personnel_id"/>
        <result property="purposeType" column="purpose_type"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="memberId" column="member_id"/>
        <result property="companyId" column="company_id"/>
        <result property="createUser" column="create_user"/>
        <result property="updateUser" column="update_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>
    
    <sql id="columns">
        <![CDATA[
        id, expo_id, business_id, business_type, appointed_personnel_id, purpose_type, remark, status, member_id, company_id, create_user, update_user, create_time, update_time, is_deleted
        ]]>
    </sql>

    <sql id="where">
        <where>
            <if test="null!=id">
                AND id = #{id}
            </if>
            <if test="null!=expoId">
                AND expo_id = #{expoId}
            </if>
            <if test="null!=businessId">
                AND business_id = #{businessId}
            </if>
            <if test="null!=businessType">
                AND business_type = #{businessType}
            </if>
            <if test="null!=appointedPersonnelId">
                AND appointed_personnel_id = #{appointedPersonnelId}
            </if>
            <if test="null!=purposeType">
                AND purpose_type = #{purposeType}
            </if>
            <if test="null!=remark and ''!=remark">
                AND remark = #{remark}
            </if>
            <if test="null!=status">
                AND status = #{status}
            </if>
            <if test="null!=memberId">
                AND member_id = #{memberId}
            </if>
            <if test="null!=companyId">
                AND company_id = #{companyId}
            </if>
            <if test="null!=createUser">
                AND create_user = #{createUser}
            </if>
            <if test="null!=updateUser">
                AND update_user = #{updateUser}
            </if>
            <if test="null!=createTime">
                AND create_time = #{createTime}
            </if>
            <if test="null!=updateTime">
                AND update_time = #{updateTime}
            </if>
            <if test="null!=isDeleted">
                AND is_deleted = #{isDeleted}
            </if>
        </where>
    </sql>

    <select id="queryAppointMyList" resultType="com.echronos.expo.model.ExpoAppointment">
        select a.* from ech_expo_appointed_personnel ap
        inner join ech_expo_appointment a on ap.id = a.appointed_personnel_id
        where ap.is_deleted = #{dto.isDeleted}
          and a.is_deleted = #{dto.isDeleted}
          and ap.expo_id = #{dto.expoId}
          and ap.business_id = #{dto.businessId}
          and ap.business_type = #{dto.businessType}
          and a.expo_id = #{dto.expoId}
        <if test="null!=dto.status">
            AND a.status = #{dto.status}
        </if>
        order by a.create_time desc;
    </select>

    <select id="queryAppointTimeList" resultType="com.echronos.expo.model.ext.ExpoAppointmentExt">
        SELECT
            a.expo_id,
            1 AS appoint_type,
            a.id,
            a.status,
            p.member_id,
            a.create_time,
            a.business_id,
            a.business_type,
            t.start_time,
            t.end_time
        FROM
            ech_expo_appointment a
                JOIN
            ech_expo_appointed_personnel p ON a.appointed_personnel_id = p.id
                LEFT JOIN
                ech_expo_appointment_time t ON a.id = t.appointment_id AND t.is_deleted = #{dto.isDeleted}
        WHERE
            a.expo_id = #{dto.expoId}
          AND a.business_id = #{dto.businessId}
          AND a.business_type = #{dto.businessType}
          AND a.is_deleted = #{dto.isDeleted}
          AND p.is_deleted = #{dto.isDeleted}

        UNION ALL

        SELECT
            a.expo_id,
            2 AS appoint_type,
            a.id,
            a.status,
            a.member_id,
            a.create_time,
            a.business_id,
            a.business_type,
            t.start_time,
            t.end_time
        FROM
            ech_expo_appointment a
                JOIN
            ech_expo_appointed_personnel p ON a.appointed_personnel_id = p.id
                LEFT JOIN
                ech_expo_appointment_time t ON a.id = t.appointment_id AND t.is_deleted = #{dto.isDeleted}
        WHERE
            p.expo_id = #{dto.expoId}
          AND p.business_id = #{dto.businessId}
          AND p.business_type = #{dto.businessType}
          AND a.is_deleted = #{dto.isDeleted}
          AND p.is_deleted = #{dto.isDeleted}
        ORDER BY
            create_time DESC, id, start_time;
    </select>

    <select id="queryDetail" resultType="com.echronos.expo.model.ext.ExpoAppointmentExt">
        select
            a.id,
            a.expo_id,
            a.business_id,
            a.business_type,
            a.purpose_type,
            a.status,
            a.remark,
            case
                when a.business_type = #{dto.businessType}
                    then 1
                else 2
                end as appoint_type,
            case
                when a.business_type = 1
                    then a.business_id
                else p.business_id
                end as exhibitor_id,
            case
                when a.business_type = #{dto.businessType}
                    then p.member_id
                else a.member_id
                end as member_id,
            case
                when a.business_type = #{dto.businessType}
                    then p.company_id
                else a.company_id
                end as company_id
        from
            ech_expo_appointment a
                join
            ech_expo_appointed_personnel p on a.appointed_personnel_id = p.id
        where
            a.id = #{dto.id}
          and a.is_deleted = #{dto.isDeleted}
          and p.is_deleted = #{dto.isDeleted}
        <if test="null!=dto.expoId">
            AND a.expo_id = #{dto.expoId}
            AND p.expo_id = #{dto.expoId}
        </if>
        <if test="null!=dto.businessId">
            AND (a.business_id = #{dto.businessId} or p.business_id = #{dto.businessId})
        </if>
    </select>

    <select id="queryNoResponse" resultType="com.echronos.expo.model.ext.ExpoAppointmentExt">
        select
            a.id,
            a.expo_id,
            a.member_id,
            p.member_id as appointed_member_id,
            a.purpose_type,
        case
            when a.business_type = 1
            then a.business_id
            else p.business_id
        end as exhibitor_id
        from
            ech_expo_appointment a
        join ech_expo_appointed_personnel p on
            a.appointed_personnel_id = p.id
        where
            a.is_deleted = #{dto.isDeleted}
            and p.is_deleted = #{dto.isDeleted}
            and a.status = #{dto.status}
            and a.create_time &lt; DATE_SUB(NOW(), interval 48 hour)
        order by
            a.create_time;
    </select>
    <select id="queryBeforeExpoNoResponse" resultType="com.echronos.expo.model.ext.ExpoAppointmentExt">
        select
            a.id,
            a.business_type,
            a.expo_id
        from
            ech_expo_appointment a
        inner join
            ech_expo_info e on
            a.expo_id = e.id
        where
            a.status = #{dto.status}
            and a.is_deleted = #{dto.isDeleted}
            and e.is_deleted = #{dto.isDeleted}
            and e.start_time is not null
            and a.create_time &lt;= DATE_SUB(e.start_time, interval 24 hour)
            and e.start_time > NOW();
    </select>

</mapper>

