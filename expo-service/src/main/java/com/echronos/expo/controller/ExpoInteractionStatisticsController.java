package com.echronos.expo.controller;

import com.echronos.commons.Result;
import com.echronos.commons.utils.CopyObjectUtils;
import com.echronos.commons.utils.RequestUserUtils;
import com.echronos.expo.dto.ExpoInteractionStatisticsDTO;
import com.echronos.expo.param.ExpoStatisticsParam;
import com.echronos.expo.service.IExpoInteractionStatisticsService;
import com.echronos.expo.vo.ExpoAppointStatusVO;
import com.echronos.expo.vo.ExpoInteractionStatisticsVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 展会互动数据统计
 *
 * <AUTHOR>
 * @date 2025/8/15 14:56
 */
@RestController
@RequestMapping("v1/expo/interaction/statistics")
public class ExpoInteractionStatisticsController {

    @Resource
    private IExpoInteractionStatisticsService expoInteractionStatisticsService;

    /**
     * 展会互动统计
     *
     * @param param 统计参数
     * @return 互动统计数据
     */
    @GetMapping(value = "/total")
    public Result<ExpoInteractionStatisticsVO> interactionStatistics(ExpoStatisticsParam param) {
        ExpoInteractionStatisticsDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInteractionStatisticsDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return Result.build(expoInteractionStatisticsService.interactionStatistics(dto));
    }

    /**
     * 预约状态分布统计
     *
     * @param param 统计参数
     * @return 预约状态分布数据
     */
    @GetMapping(value = "/appointment/status")
    public Result<ExpoAppointStatusVO> appointmentStatusDistributionStatistics(ExpoStatisticsParam param) {
        ExpoInteractionStatisticsDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInteractionStatisticsDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return Result.build(expoInteractionStatisticsService.appointmentStatusStatistics(dto));
    }

    /**
     * 每日互动趋势统计
     *
     * @param param 统计参数
     * @return 每日互动趋势数据
     */
    @GetMapping(value = "/trend")
    public Result<ExpoAppointStatusVO> interactionTrendStatistics(ExpoStatisticsParam param) {
        ExpoInteractionStatisticsDTO dto = CopyObjectUtils.copyAtoB(param, ExpoInteractionStatisticsDTO.class);
        dto.setCompanyId(RequestUserUtils.getUser().getCompanyId());
        return Result.build(expoInteractionStatisticsService.interactionTrendStatistics(dto));
    }
}
