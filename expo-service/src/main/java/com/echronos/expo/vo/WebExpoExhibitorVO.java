package com.echronos.expo.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.echronos.commons.utils.FilePathSerializer;
import com.echronos.crm.resp.SupplierCategoryResp;
import lombok.Data;

import java.util.List;

/**
 * 建站查询展商返回VO
 *
 * <AUTHOR>
 * date2025/8/6 19:32
 */
@Data
public class WebExpoExhibitorVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 展商名称
     */
    private String customerName;

    /**
     * 客户所属公司ID
     */
    private Integer customerCompanyId;

    /**
     *头像
     */
    @JSONField(serializeUsing = FilePathSerializer.class)
    private String avatar;

    /**
     * 是否开启预约：0-否 1-是
     */
    private Integer isAppoint;

    /**
     * 简介
     */
    private String blurb;

    /**
     * 供应商分类信息
     */
    private List<SupplierCategoryResp> supplierCategoryRespList;
}
