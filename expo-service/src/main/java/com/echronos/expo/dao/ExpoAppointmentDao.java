/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.echronos.expo.dto.ExpoAppointmentDTO;
import com.echronos.expo.model.ExpoAppointment;
import com.echronos.expo.model.ext.ExpoAppointmentExt;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * ExpoAppointment Dao
 * 
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface ExpoAppointmentDao extends BaseMapper<ExpoAppointment> {

    /**
     * 查询预约我的
     *
     * @param dto
     * @return
     */
    List<ExpoAppointment> queryAppointMyList(@Param("dto") ExpoAppointmentDTO dto);

    /**
     * 展会预约时间列表
     *
     * @param dto 参数
     * @return List<ExpoAppointmentExt>
     */
    List<ExpoAppointmentExt> queryAppointTimeList(@Param("dto") ExpoAppointmentDTO dto);

    /**
     * 预约详情
     *
     * @param dto 参数
     * @return ExpoAppointmentExt
     */
    ExpoAppointmentExt queryDetail(@Param("dto") ExpoAppointmentDTO dto);

    /**
     * 查询48小时内未响应的预约
     *
     * @param dto 参数
     * @return ExpoAppointmentExt
     */
    List<ExpoAppointmentExt> queryNoResponse(@Param("dto") ExpoAppointmentDTO dto);

    /**
     * 查询展前24H未响应的预约
     *
     * @param dto 参数
     * @return ExpoAppointmentExt
     */
    List<ExpoAppointmentExt> queryBeforeExpoNoResponse(@Param("dto") ExpoAppointmentDTO dto);

    /**
     * 按日期统计预约次数
     *
     * @param expoId 展会ID
     * @param companyId 公司ID
     * @return 日期和预约次数的映射
     */
    @MapKey("scanDate")
    List<Map<String, Object>> countAppointmentsByDate(@Param("expoId") Integer expoId, @Param("companyId") Integer companyId);
}
