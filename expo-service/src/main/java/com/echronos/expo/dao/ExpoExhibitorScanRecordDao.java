/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.echronos.expo.dto.ExpoInteractionStatisticsDTO;
import com.echronos.expo.model.ExpoExhibitorScanRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * ExpoExhibitorScanRecord Dao
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface ExpoExhibitorScanRecordDao extends BaseMapper<ExpoExhibitorScanRecord> {

    /**
     * 统计展商扫码次数
     *
     * @param dto 统计参数
     * @return 扫码次数
     */
    Integer countScanRecords(@Param("dto") ExpoInteractionStatisticsDTO dto);

    /**
     * 按日期统计展商扫码次数
     *
     * @param expoId 展会ID
     * @param companyId 公司ID
     * @return 日期和扫码次数的映射
     */
    List<Map<String, Object>> countScanRecordsByDate(@Param("expoId") Integer expoId, @Param("companyId") Integer companyId);
}
