/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.echronos.expo.dto.ExpoInteractionStatisticsDTO;
import com.echronos.expo.model.ExpoExhibitorScanRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ExpoExhibitorScanRecord Dao
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface ExpoExhibitorScanRecordDao extends BaseMapper<ExpoExhibitorScanRecord> {

    /**
     * 统计展商扫码次数
     *
     * @param dto 统计参数
     * @return 扫码次数
     */
    Integer countScanRecords(@Param("dto") ExpoInteractionStatisticsDTO dto);
}
