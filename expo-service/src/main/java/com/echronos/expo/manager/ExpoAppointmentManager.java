/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dao.ExpoAppointmentDao;
import com.echronos.expo.dto.ExpoAppointmentDTO;
import com.echronos.expo.enums.ExpoAppointmentStatusEnum;
import com.echronos.expo.model.ExpoAppointment;
import com.echronos.expo.model.ext.ExpoAppointmentExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * ExpoAppointment Manager
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Component
public class ExpoAppointmentManager extends ServiceImpl<ExpoAppointmentDao, ExpoAppointment> {

    @Resource
    private ExpoAppointmentDao expoAppointmentDao;

    /**
     * 根据可预约人ID查询预约列表
     *
     * @param appointedPersonnelId
     * @return
     */
    public List<ExpoAppointment> queryAppointedByPersonnelId(Integer appointedPersonnelId) {
        LambdaQueryWrapper<ExpoAppointment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExpoAppointment::getAppointedPersonnelId, appointedPersonnelId)
                .eq(ExpoAppointment::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .in(ExpoAppointment::getStatus, Arrays.asList(ExpoAppointmentStatusEnum.BE_CONFIRMED.getCode(), ExpoAppointmentStatusEnum.ACCEPTED.getCode()));
        return this.list(queryWrapper);
    }

    /**
     * 查询预约我的
     *
     * @param dto
     * @return
     */
    public List<ExpoAppointment> queryAppointMyList(ExpoAppointmentDTO dto) {
        dto.setIsDeleted(CommonStatus.DeleteEnum.NO.getValue());
        return expoAppointmentDao.queryAppointMyList(dto);
    }

    /**
     * 展会预约时间列表
     *
     * @param dto 参数
     * @return List<ExpoAppointmentExt>
     */
    public List<ExpoAppointmentExt> queryAppointTimeList(ExpoAppointmentDTO dto) {
        dto.setIsDeleted(CommonStatus.DeleteEnum.NO.getValue());
        return expoAppointmentDao.queryAppointTimeList(dto);
    }

    /**
     * 预约详情
     *
     * @param dto 参数
     * @return ExpoAppointmentExt
     */
    public ExpoAppointmentExt queryDetail(ExpoAppointmentDTO dto) {
        dto.setIsDeleted(CommonStatus.DeleteEnum.NO.getValue());
        return expoAppointmentDao.queryDetail(dto);
    }

    /**
     * 统计预约次数
     *
     * @param expoId 展会ID
     * @return 预约次数
     */
    public Integer getAppointmentCount(Integer expoId) {
        LambdaQueryWrapper<ExpoAppointment> queryWrapper = new LambdaQueryWrapper<ExpoAppointment>()
                .eq(ExpoAppointment::getExpoId, expoId)
                .eq(ExpoAppointment::getIsDeleted, CommonStatus.DeleteEnum.NO);
        return this.count(queryWrapper);
    }

    /**
     * 查询48小时内未响应的预约
     *
     * @return
     */
    public List<ExpoAppointmentExt> queryNoResponse() {
        ExpoAppointmentDTO dto = new ExpoAppointmentDTO();
        dto.setIsDeleted(CommonStatus.DeleteEnum.NO.getValue());
        dto.setStatus(ExpoAppointmentStatusEnum.BE_CONFIRMED.getCode());
        return expoAppointmentDao.queryNoResponse(dto);
    }

    /**
     * 查询展前24H未响应的预约
     *
     * @return
     */
    public List<ExpoAppointmentExt> queryBeforeExpoNoResponse() {
        ExpoAppointmentDTO dto = new ExpoAppointmentDTO();
        dto.setIsDeleted(CommonStatus.DeleteEnum.NO.getValue());
        dto.setStatus(ExpoAppointmentStatusEnum.BE_CONFIRMED.getCode());
        return expoAppointmentDao.queryBeforeExpoNoResponse(dto);
    }

    /**
     * 获取预约列表
     *
     * @param expoId 展会ID
     * @return 预约列表
     */
    public List<ExpoAppointment> getAppointmentList(Integer expoId) {
        LambdaQueryWrapper<ExpoAppointment> queryWrapper = new LambdaQueryWrapper<ExpoAppointment>()
                .eq(ExpoAppointment::getExpoId, expoId)
                .eq(ExpoAppointment::getIsDeleted, CommonStatus.DeleteEnum.NO);
        return this.list(queryWrapper);
    }

    /**
     * 按日期统计预约次数
     *
     * @param expoId 展会ID
     * @param companyId 公司ID
     * @return 日期和预约次数的映射
     */
    public List<Map<String, Object>> getAppointmentsByDate(Integer expoId, Integer companyId) {
        return expoAppointmentDao.countAppointmentsByDate(expoId, companyId);
    }
}
