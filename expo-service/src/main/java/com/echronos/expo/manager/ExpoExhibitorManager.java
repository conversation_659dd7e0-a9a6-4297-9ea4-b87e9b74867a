/**
 * Copyright © 2020 ECHRONOS Corp. All rights reserved.
 * This software is proprietary to and embodies the confidential
 * technology of ECHRONOS Corp.  Possession, use, or copying
 * of this software and media is authorized only pursuant to a
 * valid written license from ECHRONOS Corp or an authorized sublicensor.
 */
package com.echronos.expo.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.echronos.commons.enums.CommonStatus;
import com.echronos.expo.dao.ExpoExhibitorDao;
import com.echronos.expo.dto.ExpoExhibitorDTO;
import com.echronos.expo.enums.ExpoExhibitorEnums;
import com.echronos.expo.enums.ExpoFormEnums;
import com.echronos.expo.enums.ExpoReferenceFormEnum;
import com.echronos.expo.model.BaseNotTenantEntity;
import com.echronos.expo.model.ExpoExhibitor;
import com.echronos.expo.model.ext.ExpoExhibitorExt;
import com.echronos.expo.model.ext.ExpoIndexCountExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;


/**
 * ExpoExhibitor Manager
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Component
public class ExpoExhibitorManager extends ServiceImpl<ExpoExhibitorDao, ExpoExhibitor> {

    @Resource
    private ExpoExhibitorDao expoExhibitorDao;

    /**
     * 获取展商展位信息
     *
     * @param expoId     展会ID
     * @param customerId 客户ID
     * @return 展商展位信息
     */
    public ExpoExhibitor getExhibitorOne(Integer expoId, Integer customerId) {
        LambdaQueryWrapper<ExpoExhibitor> queryWrapper = new LambdaQueryWrapper<ExpoExhibitor>()
                .eq(ExpoExhibitor::getExpoId, expoId)
                .eq(ExpoExhibitor::getCustomerId, customerId)
                .eq(BaseNotTenantEntity::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.getOne(queryWrapper);
    }

    /**
     * 根据ID查询展商信息
     * @param id
     * @return
     */
    public ExpoExhibitor getByExhibitorId(Integer id){
        LambdaQueryWrapper<ExpoExhibitor> queryWrapper = new LambdaQueryWrapper<ExpoExhibitor>()
                .eq(ExpoExhibitor::getId, id)
                .eq(BaseNotTenantEntity::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.getOne(queryWrapper);
    }

    /**
     * 分页查询展商信息
     * @param page
     * @param expoExhibitorDTO
     * @return
     */
    public List<ExpoExhibitorDTO> pageList(Page page, ExpoExhibitorDTO expoExhibitorDTO){
        return expoExhibitorDao.pageList(page, expoExhibitorDTO);
    }

    /**
     * 更新展商信息
     * @param dto
     */
    public void updateInfo(ExpoExhibitorDTO dto){
        LambdaUpdateWrapper updateWrapper = new LambdaUpdateWrapper<ExpoExhibitor>()
                .eq(ExpoExhibitor::getId, dto.getId())
                .eq(BaseNotTenantEntity::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .set(ExpoExhibitor::getBusinessMemberId, dto.getBusinessMemberId())
                .set(ExpoExhibitor::getRemark, dto.getRemark())
                .set(BaseNotTenantEntity::getUpdateTime, dto.getUpdateTime())
                .set(BaseNotTenantEntity::getUpdateUser, dto.getUserId());
        this.update(updateWrapper);
    }

    /**
     * 删除展商信息
     */
    public void removeById(Integer id, Integer userId, LocalDateTime updateTime){
        LambdaUpdateWrapper updateWrapper = new LambdaUpdateWrapper<ExpoExhibitor>()
                .eq(ExpoExhibitor::getId, id)
                .eq(BaseNotTenantEntity::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .set(ExpoExhibitor::getIsDeleted, CommonStatus.DeleteEnum.YES.getValue())
                .set(BaseNotTenantEntity::getUpdateUser, userId)
                .set(BaseNotTenantEntity::getUpdateTime, updateTime);
        this.update(updateWrapper);
    }


    /**
     * 更新展商表单审核状态
     * @param exhibitorId
     * @param auditStatus
     * @param formType
     */
    public void updateExhibitorAuditStatus(Integer exhibitorId, Integer auditStatus, Integer formType) {
        if (ExpoFormEnums.FormType.EXHIBITOR_ENTERPRISE_INFO.getCode().equals(formType) ||
                ExpoFormEnums.FormType.EXHIBITOR_JOURNAL_INFO.getCode().equals(formType)) {
            ExpoExhibitor updateExhibitor = new ExpoExhibitor();
            updateExhibitor.setId(exhibitorId);
            // 更新展商表单审核状态
            Integer exhibitorAuditStatus = ExpoReferenceFormEnum.AuditStatus.AUDIT_PASS.getCode().equals(auditStatus) ?
                    ExpoExhibitorEnums.EnterpriseInfoStatus.APPROVED.code() : ExpoExhibitorEnums.EnterpriseInfoStatus.REJECTED.code();
            switch (ExpoFormEnums.FormType.getByCode(formType)) {
                case EXHIBITOR_ENTERPRISE_INFO:
                    updateExhibitor.setEnterpriseInfoStatus(exhibitorAuditStatus);
                    break;
                case EXHIBITOR_JOURNAL_INFO:
                    updateExhibitor.setJournalInfoStatus(exhibitorAuditStatus);
                    break;
            }
            this.updateById(updateExhibitor);
        }
    }

    /**
     * 建站分页查询展商信息
     *
     * @param dto
     * @return
     */
    public IPage<ExpoExhibitorExt> webExhibitorPage(ExpoExhibitorDTO dto) {
        Page<ExpoExhibitorDTO> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        return expoExhibitorDao.webExhibitorPage(page, dto);
    }

    /**
     * 官网分页查询展商信息
     * @param dto
     * @return
     */
    public IPage<ExpoExhibitorExt> queryAllExhibitorList(ExpoExhibitorDTO dto){
        Page<ExpoExhibitorDTO> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        return expoExhibitorDao.queryAllExhibitorList(page, dto);
    }

    /**
     * 查询这个展会的总数量
     * @param expoId
     * @return
     */
    public List<ExpoExhibitor> queryExhibitorNumber(Integer expoId){
        LambdaQueryWrapper<ExpoExhibitor> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExpoExhibitor::getExpoId, expoId)
                .eq(ExpoExhibitor::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue());
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 查询当前公司是否是这个展会的展商
     * @param expoId
     * @param companyId
     * @return
     */
    public ExpoExhibitor queryExhibitorByCompanyId(Integer expoId, Integer companyId){
        LambdaQueryWrapper<ExpoExhibitor> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ExpoExhibitor::getIsDeleted, CommonStatus.DeleteEnum.NO.getValue())
                .eq(ExpoExhibitor::getExpoId, expoId)
                .eq(ExpoExhibitor::getCustomerCompanyId, companyId);
        return getOne(lambdaQueryWrapper);
    }

    /**
     * 获取首页展商数量
     * @param companyId 公司ID
     * @return
     */
    public ExpoIndexCountExt getIndexExhibitorCount(Integer companyId){
        return expoExhibitorDao.getIndexExhibitorCount(companyId);
    }

}
