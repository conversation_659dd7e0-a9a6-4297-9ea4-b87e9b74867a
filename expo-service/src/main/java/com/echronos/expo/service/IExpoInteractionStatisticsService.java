package com.echronos.expo.service;

import com.echronos.expo.dto.ExpoInteractionStatisticsDTO;
import com.echronos.expo.vo.ExpoInteractionStatisticsVO;
import com.echronos.expo.vo.ExpoAppointStatusVO;

/**
 * 展会互动统计服务接口
 *
 * <AUTHOR>
 * @date 2025/8/15 14:56
 */
public interface IExpoInteractionStatisticsService {

    /**
     * 获取展会互动统计数据
     *
     * @param dto 统计参数
     * @return 互动统计结果
     */
    ExpoInteractionStatisticsVO interactionStatistics(ExpoInteractionStatisticsDTO dto);

    /**
     * 预约状态分布统计
     *
     * @param dto 统计参数
     * @return 预约状态分布结果
     */
    ExpoAppointStatusVO appointmentStatusStatistics(ExpoInteractionStatisticsDTO dto);

    /**
     * 每日互动趋势统计
     *
     * @param dto 统计参数
     * @return 每日互动趋势结果
     */
    ExpoAppointStatusVO interactionTrendStatistics(ExpoInteractionStatisticsDTO dto);
}
