package com.echronos.expo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.echronos.expo.constants.NumberConstant;
import com.echronos.expo.dto.ExpoInteractionStatisticsDTO;
import com.echronos.expo.enums.ExpoAppointmentStatusEnum;
import com.echronos.expo.manager.ExpoAppointmentManager;
import com.echronos.expo.manager.ExpoExhibitorScanRecordManager;
import com.echronos.expo.manager.ExpoInfoManager;
import com.echronos.expo.model.ExpoAppointment;
import com.echronos.expo.model.ExpoInfo;
import com.echronos.expo.service.IExpoInteractionStatisticsService;
import com.echronos.expo.vo.ExpoAppointStatusVO;
import com.echronos.expo.vo.ExpoInteractionStatisticsVO;
import com.echronos.expo.vo.ExpoInteractionTrendVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 展会互动统计服务实现类
 *
 * <AUTHOR>
 * @date 2025/8/15 14:56
 */
@Service
public class ExpoInteractionStatisticsServiceImpl implements IExpoInteractionStatisticsService {

    @Resource
    private ExpoExhibitorScanRecordManager expoExhibitorScanRecordManager;
    @Resource
    private ExpoAppointmentManager expoAppointmentManager;
    @Resource
    private ExpoInfoManager expoInfoManager;

    @Override
    public ExpoInteractionStatisticsVO interactionStatistics(ExpoInteractionStatisticsDTO dto) {
        ExpoInfo expoInfo = expoInfoManager.getById(dto.getExpoId());
        if (Objects.isNull(expoInfo)) {
            return null;
        }
        // 展商扫码次数
        Integer exhibitorScanCount = expoExhibitorScanRecordManager.getExhibitorScanRecordsCount(dto.getExpoId());
        // 观众预约次数
        Integer appointmentCount = expoAppointmentManager.getAppointmentCount(dto.getExpoId());
        int totalInteractionCount = exhibitorScanCount + appointmentCount;
        ExpoInteractionStatisticsVO vo = new ExpoInteractionStatisticsVO();
        // 展商扫码转化率
        BigDecimal exhibitorScanConversionRate = new BigDecimal(exhibitorScanCount).
                divide(new BigDecimal(totalInteractionCount), NumberConstant.FOUR, RoundingMode.HALF_UP);
        vo.setExhibitorScanConversionRate(exhibitorScanConversionRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                .setScale(NumberConstant.TWO, NumberConstant.ONE_HUNDRED));
        // 观众预约转化率
        BigDecimal audienceAppointmentConversionRate = new BigDecimal(appointmentCount).
                divide(new BigDecimal(totalInteractionCount), NumberConstant.FOUR, RoundingMode.HALF_UP);
        vo.setAudienceAppointmentConversionRate(audienceAppointmentConversionRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                .setScale(NumberConstant.TWO, NumberConstant.ONE_HUNDRED));
        vo.setAudienceAppointmentCount(appointmentCount);
        vo.setTotalInteractionCount(totalInteractionCount);
        vo.setExhibitorScanCount(exhibitorScanCount);
        return vo;
    }

    /**
     * 预约状态分布统计
     *
     * @param dto 统计参数
     * @return
     */
    @Override
    public ExpoAppointStatusVO appointmentStatusStatistics(ExpoInteractionStatisticsDTO dto) {
        List<ExpoAppointment> expoAppointmentList = expoAppointmentManager.getAppointmentList(dto.getExpoId());
        if (CollectionUtil.isEmpty(expoAppointmentList)) {
            return null;
        }
        ExpoAppointStatusVO vo = new ExpoAppointStatusVO();
        //待确认的预约数据
        List<ExpoAppointment> toConfirmList = expoAppointmentList.stream().filter(e ->
                ExpoAppointmentStatusEnum.BE_CONFIRMED.getCode().equals(e.getStatus())).collect(Collectors.toList());
        //已接受的预约数据
        List<ExpoAppointment> acceptedList = expoAppointmentList.stream().filter(e ->
                ExpoAppointmentStatusEnum.ACCEPTED.getCode().equals(e.getStatus())).collect(Collectors.toList());
        //已拒绝的预约数据
        List<ExpoAppointment> rejectedList = expoAppointmentList.stream().filter(e ->
                ExpoAppointmentStatusEnum.REJECTED.getCode().equals(e.getStatus())).collect(Collectors.toList());
        //已取消的预约数据
        List<ExpoAppointment> cancelledList = expoAppointmentList.stream().filter(e ->
                ExpoAppointmentStatusEnum.CANCELLED.getCode().equals(e.getStatus())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(toConfirmList)) {
            BigDecimal toConfirmRate = new BigDecimal(toConfirmList.size()).
                    divide(new BigDecimal(expoAppointmentList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setToConfirmRate(toConfirmRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, NumberConstant.ONE_HUNDRED));
        }
        if (CollectionUtil.isNotEmpty(acceptedList)) {
            BigDecimal acceptedRate = new BigDecimal(acceptedList.size()).
                    divide(new BigDecimal(expoAppointmentList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setAcceptedRate(acceptedRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, NumberConstant.ONE_HUNDRED));
        }
        if (CollectionUtil.isNotEmpty(rejectedList)) {
            BigDecimal rejectedRate = new BigDecimal(rejectedList.size()).
                    divide(new BigDecimal(expoAppointmentList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setRejectedRate(rejectedRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, NumberConstant.ONE_HUNDRED));
        }
        if (CollectionUtil.isNotEmpty(cancelledList)) {
            BigDecimal cancelledRate = new BigDecimal(cancelledList.size()).
                    divide(new BigDecimal(expoAppointmentList.size()), NumberConstant.FOUR, RoundingMode.HALF_UP);
            vo.setCancelledRate(cancelledRate.multiply(new BigDecimal(NumberConstant.ONE_HUNDRED))
                    .setScale(NumberConstant.TWO, NumberConstant.ONE_HUNDRED));
        }
        return vo;
    }

    /**
     * 每日互动趋势统计
     *
     * @param dto 统计参数
     * @return 每日互动趋势数据
     */
    @Override
    public ExpoInteractionTrendVO interactionTrendStatistics(ExpoInteractionStatisticsDTO dto) {
        ExpoInfo expoInfo = expoInfoManager.getById(dto.getExpoId());
        if (Objects.isNull(expoInfo)) {
            return null;
        }
        // 获取展会期间的日期范围
        LocalDate startDate = expoInfo.getStartTime().toLocalDate();
        LocalDate endDate = expoInfo.getEndTime().toLocalDate();
        // 生成日期列表
        List<String> dateList = new ArrayList<>();
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dateList.add(currentDate.format(DateTimeFormatter.ofPattern("M月d日")));
            currentDate = currentDate.plusDays(1);
        }
        // 获取展商扫码数据
        List<Map<String, Object>> scanRecords = expoExhibitorScanRecordManager.getScanRecordsByDate(dto.getExpoId(), dto.getCompanyId());
        Map<String, Integer> scanDataMap = new HashMap<>();
        for (Map<String, Object> record : scanRecords) {
            String date = record.get("scan_date").toString();
            Integer count = Integer.valueOf(record.get("scan_count").toString());
            LocalDate scanDate = LocalDate.parse(date);
            String formattedDate = scanDate.format(DateTimeFormatter.ofPattern("M月d日"));
            scanDataMap.put(formattedDate, count);
        }
        // 获取预约数据
        List<Map<String, Object>> appointmentRecords = expoAppointmentManager.getAppointmentsByDate(dto.getExpoId(), dto.getCompanyId());
        Map<String, Integer> appointmentDataMap = new HashMap<>();
        for (Map<String, Object> record : appointmentRecords) {
            String date = record.get("appointment_date").toString();
            Integer count = Integer.valueOf(record.get("appointment_count").toString());
            LocalDate appointmentDate = LocalDate.parse(date);
            String formattedDate = appointmentDate.format(DateTimeFormatter.ofPattern("M月d日"));
            appointmentDataMap.put(formattedDate, count);
        }
        List<Integer> exhibitorScanData = new ArrayList<>();
        List<Integer> appointmentData = new ArrayList<>();
        for (String date : dateList) {
            exhibitorScanData.add(scanDataMap.getOrDefault(date, 0));
            appointmentData.add(appointmentDataMap.getOrDefault(date, 0));
        }
        ExpoInteractionTrendVO vo = new ExpoInteractionTrendVO();
        vo.setDateList(dateList);
        vo.setExhibitorScanData(exhibitorScanData);
        vo.setAppointmentData(appointmentData);
        return vo;
    }

    @Override
    public ExpoInteractionTrendVO interactionHourStatistics(ExpoInteractionStatisticsDTO dto) {
        return null;
    }

}
