package com.echronos.expo.service;

import com.echronos.expo.dto.ExpoInteractionStatisticsDTO;
import com.echronos.expo.manager.ExpoAppointmentManager;
import com.echronos.expo.manager.ExpoExhibitorScanRecordManager;
import com.echronos.expo.manager.ExpoInfoManager;
import com.echronos.expo.model.ExpoInfo;
import com.echronos.expo.service.impl.ExpoInteractionStatisticsServiceImpl;
import com.echronos.expo.vo.ExpoHourlyInteractionVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

/**
 * 每小时互动分布统计服务测试类
 *
 * <AUTHOR>
 * @date 2025/8/18 17:00
 */
public class ExpoHourlyInteractionStatisticsServiceTest {

    @Mock
    private ExpoExhibitorScanRecordManager expoExhibitorScanRecordManager;

    @Mock
    private ExpoAppointmentManager expoAppointmentManager;

    @Mock
    private ExpoInfoManager expoInfoManager;

    @InjectMocks
    private ExpoInteractionStatisticsServiceImpl expoInteractionStatisticsService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testHourlyInteractionStatistics() {
        // 准备测试数据
        ExpoInteractionStatisticsDTO dto = new ExpoInteractionStatisticsDTO();
        dto.setExpoId(1);
        dto.setCompanyId(100);
        dto.setStatisticsDate(LocalDate.of(2025, 6, 15));

        // 模拟展会信息
        ExpoInfo expoInfo = new ExpoInfo();
        expoInfo.setId(1);
        expoInfo.setStartTime(LocalDateTime.of(2025, 6, 15, 9, 0));
        expoInfo.setEndTime(LocalDateTime.of(2025, 6, 18, 18, 0));

        // 模拟扫码数据
        List<Map<String, Object>> scanRecords = new ArrayList<>();
        Map<String, Object> scanRecord1 = new HashMap<>();
        scanRecord1.put("scan_hour", 9);
        scanRecord1.put("scan_count", 450);
        scanRecords.add(scanRecord1);

        Map<String, Object> scanRecord2 = new HashMap<>();
        scanRecord2.put("scan_hour", 10);
        scanRecord2.put("scan_count", 620);
        scanRecords.add(scanRecord2);

        Map<String, Object> scanRecord3 = new HashMap<>();
        scanRecord3.put("scan_hour", 11);
        scanRecord3.put("scan_count", 800);
        scanRecords.add(scanRecord3);

        // 模拟预约数据
        List<Map<String, Object>> appointmentRecords = new ArrayList<>();
        Map<String, Object> appointmentRecord1 = new HashMap<>();
        appointmentRecord1.put("appointment_hour", 12);
        appointmentRecord1.put("appointment_count", 200);
        appointmentRecords.add(appointmentRecord1);

        Map<String, Object> appointmentRecord2 = new HashMap<>();
        appointmentRecord2.put("appointment_hour", 13);
        appointmentRecord2.put("appointment_count", 350);
        appointmentRecords.add(appointmentRecord2);

        Map<String, Object> appointmentRecord3 = new HashMap<>();
        appointmentRecord3.put("appointment_hour", 14);
        appointmentRecord3.put("appointment_count", 580);
        appointmentRecords.add(appointmentRecord3);

        // 设置Mock行为
        when(expoInfoManager.getById(1)).thenReturn(expoInfo);
        when(expoExhibitorScanRecordManager.getScanRecordsByHour(anyInt(), anyInt(), any(LocalDate.class))).thenReturn(scanRecords);
        when(expoAppointmentManager.getAppointmentsByHour(anyInt(), anyInt(), any(LocalDate.class))).thenReturn(appointmentRecords);

        // 执行测试
        ExpoHourlyInteractionVO result = expoInteractionStatisticsService.hourlyInteractionStatistics(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(8, result.getHourList().size());
        assertEquals(Arrays.asList("9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00"), result.getHourList());
        
        // 验证互动数据 (扫码 + 预约)
        List<Integer> expectedData = Arrays.asList(450, 620, 800, 200, 350, 580, 0, 0);
        assertEquals(expectedData, result.getInteractionData());
    }

    @Test
    void testHourlyInteractionStatisticsWithNullDate() {
        // 准备测试数据
        ExpoInteractionStatisticsDTO dto = new ExpoInteractionStatisticsDTO();
        dto.setExpoId(1);
        dto.setCompanyId(100);
        dto.setStatisticsDate(null); // 日期为空

        // 模拟展会信息
        ExpoInfo expoInfo = new ExpoInfo();
        expoInfo.setId(1);

        // 设置Mock行为
        when(expoInfoManager.getById(1)).thenReturn(expoInfo);

        // 执行测试
        ExpoHourlyInteractionVO result = expoInteractionStatisticsService.hourlyInteractionStatistics(dto);

        // 验证结果
        assertNull(result);
    }

    @Test
    void testHourlyInteractionStatisticsWithNullExpo() {
        // 准备测试数据
        ExpoInteractionStatisticsDTO dto = new ExpoInteractionStatisticsDTO();
        dto.setExpoId(999);
        dto.setCompanyId(100);
        dto.setStatisticsDate(LocalDate.of(2025, 6, 15));

        // 设置Mock行为 - 返回null表示展会不存在
        when(expoInfoManager.getById(999)).thenReturn(null);

        // 执行测试
        ExpoHourlyInteractionVO result = expoInteractionStatisticsService.hourlyInteractionStatistics(dto);

        // 验证结果
        assertNull(result);
    }
}
