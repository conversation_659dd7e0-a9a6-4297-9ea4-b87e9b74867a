package com.echronos.expo.service;

import com.echronos.expo.dto.ExpoInteractionStatisticsDTO;
import com.echronos.expo.manager.ExpoAppointmentManager;
import com.echronos.expo.manager.ExpoExhibitorScanRecordManager;
import com.echronos.expo.manager.ExpoInfoManager;
import com.echronos.expo.model.ExpoInfo;
import com.echronos.expo.service.impl.ExpoInteractionStatisticsServiceImpl;
import com.echronos.expo.vo.ExpoInteractionTrendVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

/**
 * 展会互动统计服务测试类
 *
 * <AUTHOR>
 * @date 2025/8/18 16:00
 */
public class ExpoInteractionStatisticsServiceTest {

    @Mock
    private ExpoExhibitorScanRecordManager expoExhibitorScanRecordManager;

    @Mock
    private ExpoAppointmentManager expoAppointmentManager;

    @Mock
    private ExpoInfoManager expoInfoManager;

    @InjectMocks
    private ExpoInteractionStatisticsServiceImpl expoInteractionStatisticsService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testInteractionTrendStatistics() {
        // 准备测试数据
        ExpoInteractionStatisticsDTO dto = new ExpoInteractionStatisticsDTO();
        dto.setExpoId(1);
        dto.setCompanyId(100);

        // 模拟展会信息
        ExpoInfo expoInfo = new ExpoInfo();
        expoInfo.setId(1);
        expoInfo.setStartTime(LocalDateTime.of(2025, 6, 15, 9, 0));
        expoInfo.setEndTime(LocalDateTime.of(2025, 6, 18, 18, 0));

        // 模拟扫码数据
        List<Map<String, Object>> scanRecords = new ArrayList<>();
        Map<String, Object> scanRecord1 = new HashMap<>();
        scanRecord1.put("scan_date", "2025-06-15");
        scanRecord1.put("scan_count", 1200);
        scanRecords.add(scanRecord1);

        Map<String, Object> scanRecord2 = new HashMap<>();
        scanRecord2.put("scan_date", "2025-06-16");
        scanRecord2.put("scan_count", 1600);
        scanRecords.add(scanRecord2);

        Map<String, Object> scanRecord3 = new HashMap<>();
        scanRecord3.put("scan_date", "2025-06-17");
        scanRecord3.put("scan_count", 1100);
        scanRecords.add(scanRecord3);

        Map<String, Object> scanRecord4 = new HashMap<>();
        scanRecord4.put("scan_date", "2025-06-18");
        scanRecord4.put("scan_count", 1000);
        scanRecords.add(scanRecord4);

        // 模拟预约数据
        List<Map<String, Object>> appointmentRecords = new ArrayList<>();
        Map<String, Object> appointmentRecord1 = new HashMap<>();
        appointmentRecord1.put("appointment_date", "2025-06-15");
        appointmentRecord1.put("appointment_count", 300);
        appointmentRecords.add(appointmentRecord1);

        Map<String, Object> appointmentRecord2 = new HashMap<>();
        appointmentRecord2.put("appointment_date", "2025-06-16");
        appointmentRecord2.put("appointment_count", 400);
        appointmentRecords.add(appointmentRecord2);

        Map<String, Object> appointmentRecord3 = new HashMap<>();
        appointmentRecord3.put("appointment_date", "2025-06-17");
        appointmentRecord3.put("appointment_count", 350);
        appointmentRecords.add(appointmentRecord3);

        Map<String, Object> appointmentRecord4 = new HashMap<>();
        appointmentRecord4.put("appointment_date", "2025-06-18");
        appointmentRecord4.put("appointment_count", 250);
        appointmentRecords.add(appointmentRecord4);

        // 设置Mock行为
        when(expoInfoManager.getById(1)).thenReturn(expoInfo);
        when(expoExhibitorScanRecordManager.getScanRecordsByDate(anyInt(), anyInt())).thenReturn(scanRecords);
        when(expoAppointmentManager.getAppointmentsByDate(anyInt(), anyInt())).thenReturn(appointmentRecords);

        // 执行测试
        ExpoInteractionTrendVO result = expoInteractionStatisticsService.interactionTrendStatistics(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(4, result.getDateList().size());
        assertEquals(Arrays.asList("6月15日", "6月16日", "6月17日", "6月18日"), result.getDateList());
        assertEquals(Arrays.asList(1200, 1600, 1100, 1000), result.getExhibitorScanData());
        assertEquals(Arrays.asList(300, 400, 350, 250), result.getAppointmentData());
    }

    @Test
    void testInteractionTrendStatisticsWithNullExpo() {
        // 准备测试数据
        ExpoInteractionStatisticsDTO dto = new ExpoInteractionStatisticsDTO();
        dto.setExpoId(999);
        dto.setCompanyId(100);

        // 设置Mock行为 - 返回null表示展会不存在
        when(expoInfoManager.getById(999)).thenReturn(null);

        // 执行测试
        ExpoInteractionTrendVO result = expoInteractionStatisticsService.interactionTrendStatistics(dto);

        // 验证结果
        assertNull(result);
    }
}
