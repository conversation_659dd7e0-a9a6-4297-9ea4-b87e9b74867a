<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ech-expo</artifactId>
        <groupId>com.ech</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>expo-service</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>ech-commons</artifactId>
            <version>2.1.1${snapshot.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.springfox</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>tenant-api</artifactId>
            <version>1.0.0${snapshot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>ech-tenantfilter</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>expo-api</artifactId>
            <version>1.0.0${snapshot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>crm-api</artifactId>
            <version>1.0.7${snapshot.version}</version>
        </dependency>
        <!-- 定时任务服务 -->
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>ech-job</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!-- MQ消息服务 -->
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>ech-rabbitmq</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>iform-api</artifactId>
            <version>2.0.0${snapshot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>iform-sdk</artifactId>
            <version>1.0.0${snapshot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>nms-api</artifactId>
            <version>1.2.0${snapshot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>pms-api</artifactId>
            <version>1.0.2${snapshot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>order-api</artifactId>
            <version>1.0.5${snapshot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>search-api</artifactId>
            <version>1.0.4${snapshot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>mcs-api</artifactId>
            <version>1.0.2${snapshot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ech</groupId>
            <artifactId>ech-imc-api</artifactId>
            <version>1.0.2${snapshot.version}</version>
        </dependency>
        <!--日志升级-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- mybatisPlus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.1</version>
        </dependency>
        <!--mybatisPlus-->
        <!--SpringCloud-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!--使用OKHTTP-->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <version>${nacos.version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <!-- AOP -->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <!-- AOP -->
        <!-- swagger -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.github.jhonnymertz</groupId>
            <artifactId>java-wkhtmltopdf-wrapper</artifactId>
            <version>1.1.13-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.thymeleaf.extras</groupId>
            <artifactId>thymeleaf-extras-java8time</artifactId>
            <version>3.0.1.RELEASE</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.echronos.expo.ExpoServiceApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>
