# 每小时互动分布统计API文档

## 接口概述
该接口用于获取展会指定日期内每小时的互动分布数据，包括展商扫码和预约的合并统计。

## 接口详情

### 请求信息
- **URL**: `/v1/expo/interaction/statistics/hourly`
- **Method**: `GET`
- **Content-Type**: `application/json`

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| expoId | Integer | 是 | 展会ID |
| statisticsDate | String | 是 | 统计日期，格式：yyyy-MM-dd |

### 请求示例
```bash
GET /v1/expo/interaction/statistics/hourly?expoId=1&statisticsDate=2025-06-15
```

### 响应格式

#### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "hourList": ["9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00"],
    "interactionData": [450, 620, 800, 200, 350, 580, 450, 300]
  }
}
```

#### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 响应数据 |
| data.hourList | Array[String] | 时间段列表，格式为"H:00" |
| data.interactionData | Array[Integer] | 互动次数数据，与hourList一一对应 |

#### 错误响应
```json
{
  "code": 400,
  "message": "展会ID不能为空",
  "data": null
}
```

```json
{
  "code": 400,
  "message": "统计日期不能为空",
  "data": null
}
```

```json
{
  "code": 404,
  "message": "展会不存在",
  "data": null
}
```

## 数据说明

### 统计规则
1. **时间范围**: 固定为9:00-16:00，共8个时间段
2. **数据来源**: 
   - 展商扫码数据来自 `ech_expo_exhibitor_scan_record` 表，按创建时间的小时统计
   - 预约数据来自 `ech_expo_appointment` 和 `ech_expo_appointment_time` 表，按预约时间的开始时间小时统计
3. **统计维度**: 按小时统计，以时间的小时部分为准
4. **数据合并**: 将扫码数据和预约数据合并为总互动次数
5. **数据过滤**: 只统计未删除的记录 (`is_deleted = 0`)
6. **公司隔离**: 只统计当前用户所属公司的数据
7. **日期过滤**: 只统计指定日期的数据

### 数据对齐
- 如果某个小时没有互动数据，对应位置会填充0
- 时间段列表固定为9:00-16:00
- 两个数组（hourList、interactionData）长度相同，索引一一对应

### 表结构依赖

#### 主要表结构
1. **ech_expo_info** - 展会信息表
   - 用于验证展会是否存在
   
2. **ech_expo_exhibitor_scan_record** - 展商观众扫码记录表
   - expo_id: 展会ID
   - create_time: 扫码时间 (按小时统计)
   - company_id: 公司ID
   - is_deleted: 删除标记

3. **ech_expo_appointment** - 预约表
   - expo_id: 展会ID
   - company_id: 公司ID
   - is_deleted: 删除标记

4. **ech_expo_appointment_time** - 预约时间表
   - appointment_id: 预约ID
   - start_time: 预约开始时间 (按小时统计)
   - is_deleted: 删除标记

## 使用场景
1. **展会实时监控**: 监控展会当天各时段的互动活跃度
2. **时段分析**: 分析哪些时段互动最活跃
3. **资源调配**: 根据互动分布合理安排人员和资源
4. **数据可视化**: 为前端柱状图提供数据支持

## 前端集成示例

### JavaScript/Vue.js
```javascript
// API调用
async function getHourlyInteraction(expoId, statisticsDate) {
  try {
    const response = await fetch(`/v1/expo/interaction/statistics/hourly?expoId=${expoId}&statisticsDate=${statisticsDate}`);
    const result = await response.json();
    
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('获取每小时互动分布数据失败:', error);
    throw error;
  }
}

// 使用示例
getHourlyInteraction(1, '2025-06-15').then(data => {
  console.log('时间段列表:', data.hourList);
  console.log('互动数据:', data.interactionData);
  
  // 可以直接用于图表库如 ECharts
  const chartOption = {
    xAxis: {
      data: data.hourList
    },
    series: [{
      name: '互动次数',
      type: 'bar',
      data: data.interactionData,
      itemStyle: {
        color: '#8B7ED8' // 紫色，符合原型图
      }
    }]
  };
});
```

### 日期选择器集成
```javascript
// 日期选择器变化事件
function onDateChange(selectedDate) {
  const expoId = getCurrentExpoId();
  getHourlyInteraction(expoId, selectedDate).then(data => {
    updateChart(data);
  });
}
```

## SQL查询示例

### 展商扫码按小时统计
```sql
SELECT 
    HOUR(create_time) as scan_hour,
    COUNT(*) as scan_count
FROM ech_expo_exhibitor_scan_record
WHERE expo_id = 1
  AND company_id = 100
  AND DATE(create_time) = '2025-06-15'
  AND is_deleted = 0
GROUP BY HOUR(create_time)
ORDER BY scan_hour
```

### 预约按小时统计
```sql
SELECT 
    HOUR(t.start_time) as appointment_hour,
    COUNT(*) as appointment_count
FROM ech_expo_appointment a
INNER JOIN ech_expo_appointment_time t ON a.id = t.appointment_id
WHERE a.expo_id = 1
  AND a.company_id = 100
  AND DATE(t.start_time) = '2025-06-15'
  AND a.is_deleted = 0
  AND t.is_deleted = 0
GROUP BY HOUR(t.start_time)
ORDER BY appointment_hour
```

## 注意事项
1. 需要用户登录，接口会自动获取当前用户的公司ID
2. 展会ID和统计日期都必须提供且有效
3. 时间段固定为9:00-16:00，符合展会的一般营业时间
4. 互动数据是扫码和预约的合并统计
5. 预约统计基于预约时间表的实际预约时间，不是预约创建时间
6. 接口性能已优化，支持实时查询

## 版本历史
- v1.0: 初始版本，支持基本的每小时互动分布统计
