# 每小时互动分布统计接口实现说明

## 概述
根据提供的原型图，实现了展会每小时互动分布统计接口，用于统计指定日期内每小时的互动次数分布。

## 接口信息
- **接口路径**: `GET /v1/expo/interaction/statistics/hourly`
- **接口描述**: 获取展会指定日期内每小时的互动分布数据
- **参数**: 
  - `expoId` (展会ID，必填)
  - `statisticsDate` (统计日期，必填，格式：yyyy-MM-dd)

## 实现内容

### 1. 新增VO类
**文件**: `expo-service/src/main/java/com/echronos/expo/vo/ExpoHourlyInteractionVO.java`
```java
@Data
public class ExpoHourlyInteractionVO {
    private List<String> hourList;        // 时间段列表 (如: ["9:00", "10:00"])
    private List<Integer> interactionData; // 互动次数数据
}
```

### 2. 新增参数类
**文件**: `expo-service/src/main/java/com/echronos/expo/param/ExpoHourlyStatisticsParam.java`
```java
@Data
public class ExpoHourlyStatisticsParam {
    @NotNull(message = "{NOTNILL.EXPOID}")
    private Integer expoId;
    
    @NotNull(message = "统计日期不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate statisticsDate;
}
```

### 3. DTO扩展
**文件**: `expo-service/src/main/java/com/echronos/expo/dto/ExpoInteractionStatisticsDTO.java`
新增字段：
```java
private LocalDate statisticsDate; // 统计日期 (用于每小时互动分布统计)
```

### 4. 数据访问层扩展

#### ExpoExhibitorScanRecordDao
新增方法：
```java
List<Map<String, Object>> countScanRecordsByHour(@Param("expoId") Integer expoId, 
    @Param("companyId") Integer companyId, @Param("statisticsDate") LocalDate statisticsDate);
```

#### ExpoAppointmentDao  
新增方法：
```java
List<Map<String, Object>> countAppointmentsByHour(@Param("expoId") Integer expoId, 
    @Param("companyId") Integer companyId, @Param("statisticsDate") LocalDate statisticsDate);
```

### 5. SQL查询实现

#### 展商扫码按小时统计 (ExpoExhibitorScanRecordMapper.xml)
```sql
SELECT 
    HOUR(create_time) as scan_hour,
    COUNT(*) as scan_count
FROM ech_expo_exhibitor_scan_record
WHERE expo_id = #{expoId}
  AND company_id = #{companyId}
  AND DATE(create_time) = #{statisticsDate}
  AND is_deleted = 0
GROUP BY HOUR(create_time)
ORDER BY scan_hour
```

#### 预约按小时统计 (ExpoAppointmentMapper.xml)
```sql
SELECT 
    HOUR(t.start_time) as appointment_hour,
    COUNT(*) as appointment_count
FROM ech_expo_appointment a
INNER JOIN ech_expo_appointment_time t ON a.id = t.appointment_id
WHERE a.expo_id = #{expoId}
  AND a.company_id = #{companyId}
  AND DATE(t.start_time) = #{statisticsDate}
  AND a.is_deleted = 0
  AND t.is_deleted = 0
GROUP BY HOUR(t.start_time)
ORDER BY appointment_hour
```

### 6. 业务逻辑实现

#### Manager层扩展
- **ExpoExhibitorScanRecordManager**: 新增 `getScanRecordsByHour()` 方法
- **ExpoAppointmentManager**: 新增 `getAppointmentsByHour()` 方法

#### Service层实现
**ExpoInteractionStatisticsServiceImpl.hourlyInteractionStatistics()**:

1. 验证展会ID和统计日期的有效性
2. 生成固定的时间段列表 (9:00-16:00)
3. 查询展商扫码数据，按小时分组统计
4. 查询预约数据，按预约时间的小时分组统计
5. 将扫码和预约数据合并为总互动次数
6. 将数据按时间段对齐，缺失时段补0
7. 返回每小时互动分布数据

### 7. 控制器实现
**ExpoInteractionStatisticsController**:
新增接口方法 `/hourly`，接收 `ExpoHourlyStatisticsParam` 参数

## 数据表依赖

### 主要表结构
1. **ech_expo_info** - 展会信息表
   - 用于验证展会是否存在
   
2. **ech_expo_exhibitor_scan_record** - 展商观众扫码记录表
   - expo_id: 展会ID
   - create_time: 扫码时间 (按小时统计)
   - company_id: 公司ID
   - is_deleted: 删除标记

3. **ech_expo_appointment** - 预约表
   - expo_id: 展会ID
   - company_id: 公司ID
   - is_deleted: 删除标记

4. **ech_expo_appointment_time** - 预约时间表
   - appointment_id: 预约ID
   - start_time: 预约开始时间 (按小时统计)
   - is_deleted: 删除标记

## 返回数据格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "hourList": ["9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00"],
    "interactionData": [450, 620, 800, 200, 350, 580, 450, 300]
  }
}
```

## 测试
创建了单元测试文件 `ExpoHourlyInteractionStatisticsServiceTest.java`，包含：
- 正常情况下的每小时互动分布统计测试
- 日期为空时的异常处理测试
- 展会不存在时的异常处理测试

## 特性
1. **固定时间段**: 9:00-16:00，符合展会营业时间
2. **数据合并**: 将扫码和预约数据合并为总互动次数
3. **时间对齐**: 确保所有时间段都有对应数据，缺失数据补0
4. **精确统计**: 预约统计基于实际预约时间，不是创建时间
5. **数据完整性**: 只统计未删除的有效记录
6. **公司隔离**: 按公司ID进行数据隔离
7. **日期过滤**: 精确到指定日期的数据

## 使用示例
```bash
GET /v1/expo/interaction/statistics/hourly?expoId=1&statisticsDate=2025-06-15
```

## 技术亮点
1. **多表关联**: 预约统计涉及预约表和预约时间表的关联查询
2. **时间函数**: 使用MySQL的HOUR()函数进行小时级别统计
3. **数据合并**: 在业务层将不同来源的数据合并
4. **参数验证**: 使用JSR-303注解进行参数校验
5. **日期处理**: 支持LocalDate类型的日期参数

该接口完全基于现有的表结构实现，充分利用了预约时间表来获取准确的预约时间信息，符合原型图的展示需求。
